<script setup lang="ts"></script>

<template>
  <div class="flex min-h-screen flex-1 items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
    <div class="w-full max-w-sm space-y-10">
      <div>
        <img class="mx-auto h-12 w-auto" src="~/assets/icons/sensehawk-logo.svg" alt="SenseHawk Logo" />
        <h2 class="mt-10 text-center text-2xl/9 font-bold text-gray-900">
          Welcome to SenseHawk Console
        </h2>
      </div>
      <form class="space-y-6" action="#" method="POST">
        <div>
          <a href="#"
            class="flex w-full items-center justify-center gap-3 rounded-sm bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-xs ring-1 ring-gray-300 ring-inset hover:bg-gray-50 focus-visible:ring-transparent">
            <Icon name="logos:google-icon" class="h-5 w-5" />
            <span class="text-sm/6 font-semibold">Sign in with Google</span>
          </a>
        </div>
      </form>
    </div>
  </div>
</template>
