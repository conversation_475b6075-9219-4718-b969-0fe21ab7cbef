{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@nuxt/eslint": "1.7.0", "@nuxt/icon": "1.15.0", "@tailwindcss/vite": "^4.1.11", "eslint": "^9.0.0", "nuxt": "^4.0.1", "tailwindcss": "^4.1.11", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/tabler": "^1.2.20"}}